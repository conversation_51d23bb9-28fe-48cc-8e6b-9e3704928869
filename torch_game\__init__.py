"""
torch_game: 一个基于PyTorch的强化学习游戏框架
"""

# 导入环境模块
from torch_game.env.base_env import BaseEnv
from torch_game.env.match3_env import Match3Env

# 导入智能体模块
from torch_game.agents.base_agent import BaseAgent
from torch_game.agents.ppo_agent import PPOAgent

# 导入模型模块
from torch_game.models.policy_network import PolicyNetwork
from torch_game.models.value_network import ValueNetwork

# 导入工具模块
from torch_game.utils.replay_buffer import ReplayBuffer
from torch_game.utils.trainer import Trainer

__all__ = [
    # 环境
    'BaseEnv',
    'Match3Env',
    
    # 智能体
    'BaseAgent',
    'PPOAgent',
    
    # 模型
    'PolicyNetwork',
    'ValueNetwork',
    
    # 工具
    'ReplayBuffer',
    'Trainer'
]

__version__ = '0.1.0'